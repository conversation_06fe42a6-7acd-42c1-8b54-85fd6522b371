import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../config/colors.dart';
import '../config/models/constants.dart';
import 'locale_provider.dart';
// import 'package:youtube_player_flutter/youtube_player_flutter.dart';

class GuideScreen extends StatefulWidget {
  const GuideScreen({super.key});

  @override
  _GuideScreenState createState() => _GuideScreenState();
}

class _GuideScreenState extends State<GuideScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  // late YoutubePlayerController _youtubeController;

  // Une seule vidéo tutorielle - Commentée pour le moment
  /*final Map<String, String> _tutorial = {
    'title': 'Comment utiliser la plateforme',
    'title_ar': 'كيفية استخدام المنصة',
    'videoId': 'YOUR_VIDEO_ID',
    'description': 'Guide complet pour utiliser toutes les fonctionnalités de la plateforme',
    'description_ar': 'دليل شامل لاستخدام جميع ميزات المنصة',
  };*/

  final List<Map<String, String>> _laws = [
    {
      'title': 'Loi n° 12.90 relative à l\'urbanisme',
      'title_ar': 'القانون رقم 12.90 المتعلق بالتعمير',
      'content': '''
Article 1 : La présente loi a pour objet de définir les différents documents d'urbanisme...

Article 2 : Le schéma directeur d'aménagement urbain planifie l'organisation générale...
      ''',
      'content_ar': '''
المادة 1: يهدف هذا القانون إلى تحديد وثائق التعمير المختلفة...

المادة 2: يخطط المخطط التوجيهي للتهيئة العمرانية التنظيم العام...
      ''',
    },
    {
      'title': 'Loi n° 25.90 relative aux lotissements',
      'title_ar': 'القانون رقم 25.90 المتعلق بالتجزئات',
      'content': '''
Article 1 : Est considéré comme lotissement toute division par vente...

Article 2 : La création d'un lotissement est subordonnée à l'obtention...
      ''',
      'content_ar': '''
المادة 1: يعتبر تجزئة كل تقسيم عن طريق البيع...

المادة 2: يخضع إحداث التجزئة للحصول على...
      ''',
    },
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 1, vsync: this); // Changed to 1 tab
    /*_youtubeController = YoutubePlayerController(
      initialVideoId: _tutorial['videoId']!,
      flags: const YoutubePlayerFlags(
        autoPlay: false,
        mute: false,
      ),
    );*/
  }

  @override
  void dispose() {
    _tabController.dispose();
    //_youtubeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final localeProvider = Provider.of<LocaleProvider>(context);
    final isArabic = localeProvider.locale.languageCode == 'ar';

    return Scaffold(
      backgroundColor: AppColors.lightGray,
      appBar: AppBar(
        title: Text(
          isArabic ? 'دليل الاستخدام' : 'Guide d\'utilisation',
          style: const TextStyle(color: AppColors.textLight),
        ),
        backgroundColor: AppColors.primaryOrange,
        // Removed TabBar since we only have one tab now
      ),
      body: _buildLawsTab(isArabic), // Directly show laws content
    );
  }

  /* Commented out tutorial tab
  Widget _buildTutorialTab(bool isArabic) {
    return SingleChildScrollView(
      padding: Constants.screenPadding,
      child: Card(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            AspectRatio(
              aspectRatio: 16 / 9,
              child: YoutubePlayer(
                controller: _youtubeController,
                showVideoProgressIndicator: true,
                progressIndicatorColor: AppColors.primaryOrange,
                progressColors: const ProgressBarColors(
                  playedColor: AppColors.primaryOrange,
                  handleColor: AppColors.primaryOrange,
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    isArabic ? _tutorial['title_ar']! : _tutorial['title']!,
                    style: Constants.sectionTitleStyle,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    isArabic ? _tutorial['description_ar']! : _tutorial['description']!,
                    style: Constants.subtitleStyle,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }*/

  Widget _buildLawsTab(bool isArabic) {
    return ListView.builder(
      padding: Constants.screenPadding,
      itemCount: _laws.length,
      itemBuilder: (context, index) {
        final law = _laws[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 16),
          child: ExpansionTile(
            title: Text(
              isArabic ? law['title_ar']! : law['title']!,
              style: Constants.sectionTitleStyle,
            ),
            children: [
              Padding(
                padding: const EdgeInsets.all(16),
                child: Text(
                  isArabic ? law['content_ar']! : law['content']!,
                  style: Constants.subtitleStyle,
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}


