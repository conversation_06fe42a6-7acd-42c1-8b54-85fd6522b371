class Request {
  final String id;
  final String type;
  final double surface;
  final String status;
  final double fees;
  final bool isPaid;

  Request({
    required this.id,
    required this.type,
    required this.surface,
    required this.status,
    required this.fees,
    this.isPaid = false,
  });

  factory Request.fromJson(Map<String, dynamic> json) {
    return Request(
      id: json['id'],
      type: json['type'],
      surface: json['surface'],
      status: json['status'],
      fees: json['fees'] ?? 0.0,
      isPaid: json['isPaid'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type,
      'surface': surface,
      'status': status,
      'fees': fees,
      'isPaid': isPaid,
    };
  }
}