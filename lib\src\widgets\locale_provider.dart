import 'package:flutter/material.dart';

class LocaleProvider extends ChangeNotifier {
  Locale _locale = const Locale('ar', 'SA'); // Default to Arabic

  Locale get locale => _locale;

  void setLocale(Locale locale) {
    if (_locale == locale) return; // Avoid unnecessary updates
    if (locale.languageCode == 'ar' || locale.languageCode == 'fr') {
      _locale = locale;
      notifyListeners(); // Trigger UI rebuild with new locale
    }
  }
}