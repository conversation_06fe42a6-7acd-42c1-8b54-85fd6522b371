import 'package:flutter/material.dart';

class AppColors {
  // Primary Brand Colors
  static const Color primaryOrange = Color(0xFFFF5722); // Main brand color
  static const Color pureBlack = Color(0xFF000000);
  static const Color pureWhite = Color(0xFFFFFFFF);

  // Orange Variations
  static const Color orangeLight = Color(0xFFFF8A65); // Lighter shade
  static const Color orangeDark = Color(0xFFE64A19);  // Darker shade
  static const Color orangePale = Color(0xFFFFEBE7);  // Very light orange for backgrounds

  // Neutral Colors - Light Theme
  static const Color darkGray = Color(0xFF212121);    // For text
  static const Color mediumGray = Color(0xFF757575);  // For secondary text
  static const Color lightGray = Color(0xFFF5F5F5);   // For backgrounds
  static const Color borderGray = Color(0xFFE0E0E0);  // For borders

  // Neutral Colors - Dark Theme
  static const Color darkBackground = Color(0xFF121212);     // Dark background
  static const Color darkSurface = Color(0xFF1E1E1E);       // Dark surface
  static const Color darkCard = Color(0xFF2D2D2D);          // Dark card background
  static const Color darkBorder = Color(0xFF404040);        // Dark borders
  static const Color lightText = Color(0xFFE0E0E0);         // Light text for dark theme
  static const Color dimText = Color(0xFFB0B0B0);           // Dimmed text for dark theme

  // Status Colors
  static const Color success = Color(0xFF4CAF50);     // Green for success
  static const Color error = Color(0xFFD32F2F);       // Red for errors
  static const Color warning = Color(0xFFFFA726);     // Orange for warnings
  static const Color info = Color(0xFF2196F3);        // Blue for info

  // Light Theme Colors
  static const LightThemeColors light = LightThemeColors();

  // Dark Theme Colors
  static const DarkThemeColors dark = DarkThemeColors();

  // Backward compatibility - these will be replaced by theme-aware colors
  static const Color backgroundPrimary = pureWhite;
  static const Color backgroundSecondary = lightGray;
  static const Color backgroundAccent = orangePale;
  static const Color textPrimary = pureBlack;
  static const Color textSecondary = darkGray;
  static const Color textLight = pureWhite;
  static const Color textAccent = primaryOrange;
  static const Color buttonPrimary = primaryOrange;
  static const Color buttonSecondary = pureBlack;
  static const Color buttonDisabled = mediumGray;
  static const Color borderPrimary = borderGray;
  static const Color borderFocused = primaryOrange;
  static const Color borderError = error;
  static Color shadowColor = pureBlack.withOpacity(0.1);
  static Color overlayColor = pureBlack.withOpacity(0.5);
}

class LightThemeColors {
  const LightThemeColors();

  // Background Colors
  Color get backgroundPrimary => AppColors.pureWhite;
  Color get backgroundSecondary => AppColors.lightGray;
  Color get backgroundAccent => AppColors.orangePale;
  Color get surface => AppColors.pureWhite;
  Color get card => AppColors.pureWhite;

  // Text Colors
  Color get textPrimary => AppColors.pureBlack;
  Color get textSecondary => AppColors.darkGray;
  Color get textAccent => AppColors.primaryOrange;
  Color get textOnPrimary => AppColors.pureWhite;

  // Button Colors
  Color get buttonPrimary => AppColors.primaryOrange;
  Color get buttonSecondary => AppColors.pureBlack;
  Color get buttonDisabled => AppColors.mediumGray;

  // Border Colors
  Color get borderPrimary => AppColors.borderGray;
  Color get borderFocused => AppColors.primaryOrange;
  Color get borderError => AppColors.error;

  // Shadow Colors
  Color get shadowColor => AppColors.pureBlack.withOpacity(0.1);
  Color get overlayColor => AppColors.pureBlack.withOpacity(0.5);
}

class DarkThemeColors {
  const DarkThemeColors();

  // Background Colors
  Color get backgroundPrimary => AppColors.darkBackground;
  Color get backgroundSecondary => AppColors.darkSurface;
  Color get backgroundAccent => AppColors.darkCard;
  Color get surface => AppColors.darkSurface;
  Color get card => AppColors.darkCard;

  // Text Colors
  Color get textPrimary => AppColors.lightText;
  Color get textSecondary => AppColors.dimText;
  Color get textAccent => AppColors.primaryOrange;
  Color get textOnPrimary => AppColors.pureWhite;

  // Button Colors
  Color get buttonPrimary => AppColors.primaryOrange;
  Color get buttonSecondary => AppColors.darkCard;
  Color get buttonDisabled => AppColors.mediumGray;

  // Border Colors
  Color get borderPrimary => AppColors.darkBorder;
  Color get borderFocused => AppColors.primaryOrange;
  Color get borderError => AppColors.error;

  // Shadow Colors
  Color get shadowColor => AppColors.pureBlack.withOpacity(0.3);
  Color get overlayColor => AppColors.pureBlack.withOpacity(0.7);
}
