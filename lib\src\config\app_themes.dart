import 'package:flutter/material.dart';
import 'colors.dart';

class AppThemes {
  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.light,
      fontFamily: 'Cairo',
      
      // Color Scheme
      colorScheme: ColorScheme.light(
        primary: AppColors.primaryOrange,
        onPrimary: AppColors.light.textOnPrimary,
        secondary: AppColors.orangeLight,
        onSecondary: AppColors.light.textOnPrimary,
        surface: AppColors.light.surface,
        onSurface: AppColors.light.textPrimary,
        background: AppColors.light.backgroundPrimary,
        onBackground: AppColors.light.textPrimary,
        error: AppColors.error,
        onError: AppColors.pureWhite,
        outline: AppColors.light.borderPrimary,
        shadow: AppColors.light.shadowColor,
      ),

      // Scaffold
      scaffoldBackgroundColor: AppColors.light.backgroundPrimary,

      // AppBar Theme
      appBarTheme: AppBarTheme(
        backgroundColor: AppColors.primaryOrange,
        foregroundColor: AppColors.light.textOnPrimary,
        elevation: 0,
        centerTitle: true,
        titleTextStyle: const TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.bold,
          color: AppColors.pureWhite,
          fontFamily: 'Cairo',
        ),
        iconTheme: const IconThemeData(
          color: AppColors.pureWhite,
        ),
      ),

      // Card Theme
      cardTheme: CardTheme(
        color: AppColors.light.card,
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        shadowColor: AppColors.light.shadowColor,
      ),

      // Elevated Button Theme
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.light.buttonPrimary,
          foregroundColor: AppColors.light.textOnPrimary,
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 2,
          textStyle: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            fontFamily: 'Cairo',
            letterSpacing: 0.5,
          ),
        ),
      ),

      // Text Button Theme
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: AppColors.light.textAccent,
          textStyle: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            fontFamily: 'Cairo',
          ),
        ),
      ),

      // Input Decoration Theme
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: AppColors.light.surface,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: AppColors.light.borderPrimary,
            width: 1.5,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: AppColors.light.borderPrimary,
            width: 1.5,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: AppColors.light.borderFocused,
            width: 3.0,
          ),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: AppColors.light.borderError,
            width: 1.5,
          ),
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        labelStyle: TextStyle(
          color: AppColors.light.textSecondary,
          fontFamily: 'Cairo',
        ),
        hintStyle: TextStyle(
          color: AppColors.light.textSecondary.withOpacity(0.6),
          fontFamily: 'Cairo',
        ),
      ),

      // Text Theme
      textTheme: TextTheme(
        displayLarge: TextStyle(
          fontSize: 32,
          fontWeight: FontWeight.bold,
          color: AppColors.light.textPrimary,
          fontFamily: 'Cairo',
          height: 1.2,
        ),
        headlineLarge: TextStyle(
          fontSize: 24,
          fontWeight: FontWeight.bold,
          color: AppColors.light.textPrimary,
          fontFamily: 'Cairo',
          height: 1.3,
        ),
        bodyLarge: TextStyle(
          fontSize: 18,
          color: AppColors.light.textSecondary,
          fontFamily: 'Cairo',
          height: 1.5,
        ),
        bodyMedium: TextStyle(
          fontSize: 16,
          color: AppColors.light.textPrimary,
          fontFamily: 'Cairo',
          height: 1.4,
        ),
        labelLarge: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.bold,
          color: AppColors.light.textOnPrimary,
          fontFamily: 'Cairo',
          letterSpacing: 0.5,
        ),
      ),

      // Icon Theme
      iconTheme: IconThemeData(
        color: AppColors.light.textPrimary,
        size: 24,
      ),

      // Divider Theme
      dividerTheme: DividerThemeData(
        color: AppColors.light.borderPrimary,
        thickness: 1,
      ),
    );
  }

  static ThemeData get darkTheme {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.dark,
      fontFamily: 'Cairo',
      
      // Color Scheme
      colorScheme: ColorScheme.dark(
        primary: AppColors.primaryOrange,
        onPrimary: AppColors.dark.textOnPrimary,
        secondary: AppColors.orangeLight,
        onSecondary: AppColors.dark.textOnPrimary,
        surface: AppColors.dark.surface,
        onSurface: AppColors.dark.textPrimary,
        background: AppColors.dark.backgroundPrimary,
        onBackground: AppColors.dark.textPrimary,
        error: AppColors.error,
        onError: AppColors.pureWhite,
        outline: AppColors.dark.borderPrimary,
        shadow: AppColors.dark.shadowColor,
      ),

      // Scaffold
      scaffoldBackgroundColor: AppColors.dark.backgroundPrimary,

      // AppBar Theme
      appBarTheme: AppBarTheme(
        backgroundColor: AppColors.primaryOrange,
        foregroundColor: AppColors.dark.textOnPrimary,
        elevation: 0,
        centerTitle: true,
        titleTextStyle: const TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.bold,
          color: AppColors.pureWhite,
          fontFamily: 'Cairo',
        ),
        iconTheme: const IconThemeData(
          color: AppColors.pureWhite,
        ),
      ),

      // Card Theme
      cardTheme: CardTheme(
        color: AppColors.dark.card,
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        shadowColor: AppColors.dark.shadowColor,
      ),

      // Elevated Button Theme
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.dark.buttonPrimary,
          foregroundColor: AppColors.dark.textOnPrimary,
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 2,
          textStyle: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            fontFamily: 'Cairo',
            letterSpacing: 0.5,
          ),
        ),
      ),

      // Text Button Theme
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: AppColors.dark.textAccent,
          textStyle: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            fontFamily: 'Cairo',
          ),
        ),
      ),

      // Input Decoration Theme
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: AppColors.dark.surface,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: AppColors.dark.borderPrimary,
            width: 1.5,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: AppColors.dark.borderPrimary,
            width: 1.5,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: AppColors.dark.borderFocused,
            width: 3.0,
          ),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: AppColors.dark.borderError,
            width: 1.5,
          ),
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        labelStyle: TextStyle(
          color: AppColors.dark.textSecondary,
          fontFamily: 'Cairo',
        ),
        hintStyle: TextStyle(
          color: AppColors.dark.textSecondary.withOpacity(0.6),
          fontFamily: 'Cairo',
        ),
      ),

      // Text Theme
      textTheme: TextTheme(
        displayLarge: TextStyle(
          fontSize: 32,
          fontWeight: FontWeight.bold,
          color: AppColors.dark.textPrimary,
          fontFamily: 'Cairo',
          height: 1.2,
        ),
        headlineLarge: TextStyle(
          fontSize: 24,
          fontWeight: FontWeight.bold,
          color: AppColors.dark.textPrimary,
          fontFamily: 'Cairo',
          height: 1.3,
        ),
        bodyLarge: TextStyle(
          fontSize: 18,
          color: AppColors.dark.textSecondary,
          fontFamily: 'Cairo',
          height: 1.5,
        ),
        bodyMedium: TextStyle(
          fontSize: 16,
          color: AppColors.dark.textPrimary,
          fontFamily: 'Cairo',
          height: 1.4,
        ),
        labelLarge: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.bold,
          color: AppColors.dark.textOnPrimary,
          fontFamily: 'Cairo',
          letterSpacing: 0.5,
        ),
      ),

      // Icon Theme
      iconTheme: IconThemeData(
        color: AppColors.dark.textPrimary,
        size: 24,
      ),

      // Divider Theme
      dividerTheme: DividerThemeData(
        color: AppColors.dark.borderPrimary,
        thickness: 1,
      ),
    );
  }
}
