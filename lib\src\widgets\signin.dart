import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../config/models/constants.dart';
import '../config/theme_helper.dart';
import '../widgets/locale_provider.dart';
import '../widgets/dashboard.dart';
import '../widgets/signup.dart';

class SignInScreen extends StatefulWidget {
  const SignInScreen({super.key});

  @override
  State<SignInScreen> createState() => _SignInScreenState();
}

class _SignInScreenState extends State<SignInScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  String? _validateEmail(String? value, bool isArabic) {
    if (value == null || value.isEmpty) {
      return isArabic ? 'يرجى إدخال البريد الإلكتروني' : 'Veuillez entrer votre email';
    }
    final emailRegex = RegExp(r'^[^@]+@[^@]+\.[^@]+');
    if (!emailRegex.hasMatch(value)) {
      return isArabic ? 'البريد الإلكتروني غير صالح' : 'Email invalide';
    }
    return null;
  }

  String? _validatePassword(String? value, bool isArabic) {
    if (value == null || value.isEmpty) {
      return isArabic ? 'يرجى إدخال كلمة المرور' : 'Veuillez entrer votre mot de passe';
    }
    if (value.length < 8) {
      return isArabic ? 'كلمة المرور قصيرة' : 'Le mot de passe est trop court';
    }
    return null;
  }

  void _handleSignIn(BuildContext context) {
    if (_formKey.currentState!.validate()) {
      Navigator.push(
        context,
        MaterialPageRoute(builder: (context) => const DashboardScreen()),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final localeProvider = Provider.of<LocaleProvider>(context);
    final isArabic = localeProvider.locale.languageCode == 'ar';

    final colors = ThemeHelper.getColorsWithListener(context);

    return Scaffold(
      backgroundColor: colors.backgroundSecondary,
      body: Padding(
        padding: Constants.screenPadding,
        child: SingleChildScrollView(
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                _buildPlatformHeader(isArabic),
                const SizedBox(height: Constants.extraLargeSpacing),
                _buildLoginCard(isArabic),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPlatformHeader(bool isArabic) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(
          isArabic ? 'منصة بناء' : 'Plateforme Bâtir',
          style: ThemeHelper.getTitleStyle(context),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: Constants.smallSpacing),
        Text(
          isArabic ? 'اهلا بك في منصة بناء' : 'Bienvenue sur la plateforme Bâtir',
          style: ThemeHelper.getSubtitleStyle(context),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildLoginCard(bool isArabic) {
    final colors = ThemeHelper.getColors(context);

    return Card(
      elevation: Constants.cardTheme.elevation,
      shape: Constants.cardTheme.shape,
      color: colors.card,
        child: Padding(
          padding: Constants.cardPadding,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              _buildLoginHeader(isArabic),
              const SizedBox(height: Constants.largeSpacing),
              _buildEmailField(isArabic),
              const SizedBox(height: Constants.mediumSpacing),
              _buildPasswordField(isArabic),
              const SizedBox(height: Constants.largeSpacing),
              _buildLoginButton(isArabic),
              const SizedBox(height: Constants.mediumSpacing),
              _buildFooterLinks(isArabic),
            ],
          ),
        ),
    );
  }

  Widget _buildLoginHeader(bool isArabic) => Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Text(
            isArabic ? 'تسجيل الدخول' : 'Connexion',
            style: ThemeHelper.getSectionTitleStyle(context),
            textAlign: isArabic ? TextAlign.right : TextAlign.left,
          ),
          const SizedBox(height: Constants.smallSpacing),
          Text(
            isArabic
                ? 'أدخل بريدك الإلكتروني وكلمة المرور للوصول إلى حسابك'
                : 'Entrez votre email et mot de passe pour accéder à votre compte',
            style: ThemeHelper.getSubtitleStyle(context),
            textAlign: isArabic ? TextAlign.right : TextAlign.left,
          ),
        ],
      );

  Widget _buildEmailField(bool isArabic) => TextFormField(
        controller: _emailController,
        textAlign: isArabic ? TextAlign.right : TextAlign.left,
        validator: (value) => _validateEmail(value, isArabic),
        keyboardType: TextInputType.emailAddress,
        decoration: ThemeHelper.getInputDecoration(
          context,
          labelText: isArabic ? 'البريد الإلكتروني *' : 'Email *',
          hintText: '<EMAIL>',
        ),
      );

  Widget _buildPasswordField(bool isArabic) => TextFormField(
        controller: _passwordController,
        obscureText: true,
        textAlign: isArabic ? TextAlign.right : TextAlign.left,
        validator: (value) => _validatePassword(value, isArabic),
        decoration: ThemeHelper.getInputDecoration(
          context,
          labelText: isArabic ? 'كلمة المرور *' : 'Mot de passe *',
          hintText: '********',
        ),
      );

  Widget _buildLoginButton(bool isArabic) => SizedBox(
        width: double.infinity,
        child: Builder(
          builder: (context) => ElevatedButton(
            onPressed: () => _handleSignIn(context),
            style: ThemeHelper.getPrimaryButtonStyle(context),
            child: Text(
              isArabic ? 'تسجيل الدخول' : 'Connexion',
              style: ThemeHelper.getButtonTextStyle(context),
            ),
          ),
        ),
      );

  Widget _buildFooterLinks(bool isArabic) => Column(
        children: [
          Align(
            alignment: Alignment.center,
            child: Builder(
              builder: (context) {
                final colors = ThemeHelper.getColors(context);
                return Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      isArabic ? 'ليس لديك حساب؟' : 'Pas de compte ?',
                      style: ThemeHelper.getSubtitleStyle(context),
                    ),
                    TextButton(
                      onPressed: () => Navigator.push(
                        context,
                        MaterialPageRoute(builder: (context) => const SignUpScreen()),
                      ),
                      child: Text(
                        isArabic ? 'إنشاء حساب' : 'Créer un compte',
                        style: ThemeHelper.getSubtitleStyle(context).copyWith(
                          color: colors.textAccent,
                        ),
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
        ],
      );
}
