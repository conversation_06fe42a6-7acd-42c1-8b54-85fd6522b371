import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

enum AppThemeMode { light, dark, system }

class ThemeProvider extends ChangeNotifier {
  AppThemeMode _themeMode = AppThemeMode.system;
  bool _isDarkMode = false;

  AppThemeMode get themeMode => _themeMode;
  bool get isDarkMode => _isDarkMode;

  ThemeProvider() {
    _loadThemeFromPrefs();
    _updateThemeBasedOnSystem();
  }

  void setThemeMode(AppThemeMode mode) {
    if (_themeMode == mode) return;
    _themeMode = mode;
    _updateThemeBasedOnSystem();
    _saveThemeToPrefs();
    notifyListeners();
  }

  void _updateThemeBasedOnSystem() {
    switch (_themeMode) {
      case AppThemeMode.light:
        _isDarkMode = false;
        break;
      case AppThemeMode.dark:
        _isDarkMode = true;
        break;
      case AppThemeMode.system:
        // This will be updated by the system brightness
        final brightness = WidgetsBinding.instance.platformDispatcher.platformBrightness;
        _isDarkMode = brightness == Brightness.dark;
        break;
    }
  }

  void updateSystemBrightness(Brightness brightness) {
    if (_themeMode == AppThemeMode.system) {
      final newIsDarkMode = brightness == Brightness.dark;
      if (_isDarkMode != newIsDarkMode) {
        _isDarkMode = newIsDarkMode;
        notifyListeners();
      }
    }
  }

  Future<void> _loadThemeFromPrefs() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final themeModeIndex = prefs.getInt('theme_mode') ?? AppThemeMode.system.index;
      _themeMode = AppThemeMode.values[themeModeIndex];
      _updateThemeBasedOnSystem();
    } catch (e) {
      // If there's an error loading preferences, use default
      _themeMode = AppThemeMode.system;
      _updateThemeBasedOnSystem();
    }
  }

  Future<void> _saveThemeToPrefs() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt('theme_mode', _themeMode.index);
    } catch (e) {
      // Handle error silently
    }
  }
}
