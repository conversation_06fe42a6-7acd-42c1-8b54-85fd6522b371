import 'package:droit/src/widgets/guide_screen.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../config/colors.dart';
import '../config/models/constants.dart';
import 'base_screen.dart';
import 'locale_provider.dart';
import 'NewRequestScreen.dart';
import 'history_screen.dart';

class DashboardScreen extends StatelessWidget {
  const DashboardScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return const BaseScreen(
      currentIndex: 0,
      child: DashboardContent(),
    );
  }
}

class DashboardContent extends StatelessWidget {
  const DashboardContent({super.key});

  @override
  Widget build(BuildContext context) {
    final localeProvider = Provider.of<LocaleProvider>(context);
    final isArabic = localeProvider.locale.languageCode == 'ar';

    return Padding(
      padding: Constants.screenPadding,
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: isArabic ? CrossAxisAlignment.end : CrossAxisAlignment.start,
          children: [
            _buildWelcomeHeader(isArabic),
            const SizedBox(height: Constants.extraLargeSpacing),
            _buildMainActionsRow(isArabic, context),
          ],
        ),
      ),
    );
  }

  Widget _buildWelcomeHeader(bool isArabic) {
    return Column(
      crossAxisAlignment: isArabic ? CrossAxisAlignment.start : CrossAxisAlignment.start,
      children: [
        Text(
          isArabic ? 'مرحبا بك في منصة بناء' : 'Bienvenue sur la plateforme Bâtir',
          style: Constants.titleStyle,
          textAlign: isArabic ? TextAlign.right : TextAlign.left,
        ),
        const SizedBox(height: Constants.smallSpacing),
        Text(
          isArabic
              ? 'مرحبًا في منصة بناء لإدارة طلبات وتراخيص البناء. يمكنك متابعة طلباتك وإدارة تراخيصك من هنا.'
              : 'Bienvenue sur la plateforme Bâtir pour gérer vos demandes et permis de construire. Suivez vos demandes et gérez vos permis ici.',
          style: Constants.subtitleStyle,
          textAlign: isArabic ? TextAlign.right : TextAlign.left,
        ),
      ],
    );
  }

  Widget _buildMainActionsRow(bool isArabic, BuildContext context) {
    return Column(
      children: [
        _buildActionCard(
          context: context,
          icon: Icons.add_circle_outline,
          iconColor: AppColors.success,
          title: isArabic ? 'تقديم طلب' : 'Nouvelle demande',
          subtitle: isArabic
              ? 'قم بتقديم طلب رخصة بناء'
              : 'Soumettez une demande de permis',
          buttonLabel: isArabic ? 'تقديم طلب' : 'Soumettre',
          onButtonPressed: () {
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const NewRequestScreen()),
            );
          },
        ),
        const SizedBox(height: Constants.mediumSpacing),
        _buildActionCard(
          context: context,
          icon: Icons.autorenew,
          iconColor: AppColors.info,
          title: isArabic ? 'تجديد الطلب' : 'Renouveler une demande',
          subtitle: isArabic
              ? 'قم بتجديد طلب رخصة بناء قديم'
              : 'Renouvelez une demande de permis existante',
          buttonLabel: isArabic ? 'تقديم طلب' : 'Soumettre',
          onButtonPressed: () {}, // À implémenter
        ),
        const SizedBox(height: Constants.mediumSpacing),
        _buildActionCard(
          context: context,
          icon: Icons.list_alt,
          iconColor: AppColors.warning,
          title: isArabic ? 'الطلبات' : 'Demandes',
          subtitle: isArabic
              ? 'عرض كل الطلبات التي قمت بتقديمها'
              : 'Voir toutes vos demandes soumises',
          buttonLabel: isArabic ? 'عرض الطلبات' : 'Voir les demandes',
          onButtonPressed: () {
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const HistoryScreen()),
            );
          },
        ),
        const SizedBox(height: Constants.mediumSpacing),
        _buildActionCard(
          context: context,
          icon: Icons.help_outline,
          iconColor: AppColors.primaryOrange,
          title: isArabic ? 'دليل الاستخدام' : 'Guide d\'utilisation',
          subtitle: isArabic
              ? 'تعرف على كيفية استخدام منصة بناء'
              : 'Apprenez à utiliser la plateforme Bâtir',
          buttonLabel: isArabic ? 'عرض الدليل' : 'Voir le guide',
          onButtonPressed: () {
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const GuideScreen()),
            );
          },
        ),
      ],
    );
  }

  Widget _buildActionCard({
    required BuildContext context,
    required IconData icon,
    required Color iconColor,
    required String title,
    required String subtitle,
    required String buttonLabel,
    VoidCallback? onButtonPressed,
  }) {
    return Card(
      elevation: Constants.cardTheme.elevation,
      shape: Constants.cardTheme.shape,
      color: Constants.cardTheme.color,
      child: Container(
        width: double.infinity,
        padding: Constants.cardPadding,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: iconColor.withOpacity(0.1),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(icon, color: iconColor, size: 28),
                ),
                const SizedBox(width: Constants.smallSpacing),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: Constants.sectionTitleStyle.copyWith(
                          fontSize: 16,
                          height: 1.2,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: Constants.smallSpacing),
                      Text(
                        subtitle,
                        style: Constants.subtitleStyle.copyWith(
                          fontSize: 14,
                          height: 1.2,
                        ),
                        maxLines: 3,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: Constants.mediumSpacing),
            Align(
              alignment: Alignment.centerLeft, // Changé de centerRight à centerLeft
              child: TextButton(
                onPressed: onButtonPressed ?? () {},
                style: TextButton.styleFrom(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                  backgroundColor: AppColors.primaryOrange.withOpacity(0.1),
                ),
                child: Text(
                  buttonLabel,
                  style: const TextStyle(
                    color: AppColors.primaryOrange,
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

