import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../config/colors.dart';
import '../config/models/constants.dart';
import '../widgets/locale_provider.dart';

class NewRequestScreen extends StatefulWidget {
  const NewRequestScreen({super.key});

  @override
  _NewRequestScreenState createState() => _NewRequestScreenState();
}

class _NewRequestScreenState extends State<NewRequestScreen> {
  int _currentStep = 1;
  bool _isOwnerApplicant = false;
  String? _propertyType;
  String? _requestType;
  final TextEditingController _ownerNameController = TextEditingController();
  final TextEditingController _ownerAddressController = TextEditingController();
  final TextEditingController _ownerEmailController = TextEditingController();
  final TextEditingController _projectAddressController = TextEditingController();
  final TextEditingController _totalAreaController = TextEditingController();
  final TextEditingController _requestNatureController = TextEditingController();
  final TextEditingController _currentLandUseController = TextEditingController();
  final TextEditingController _existingBuildingsController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  final List<String> _selectedFiles = []; // Simplified file list

  @override
  void dispose() {
    _ownerNameController.dispose();
    _ownerAddressController.dispose();
    _ownerEmailController.dispose();
    _projectAddressController.dispose();
    _totalAreaController.dispose();
    _requestNatureController.dispose();
    _currentLandUseController.dispose();
    _existingBuildingsController.dispose();
    super.dispose();
  }

  void _nextStep() {
    if (_formKey.currentState!.validate()) {
      setState(() {
        if (_currentStep < 4) {
          _currentStep += 1;
        } else {
          // Gérer la soumission finale ici
          _submitForm();
        }
      });
    }
  }

  void _previousStep() {
    setState(() {
      if (_currentStep > 1) {
        _currentStep -= 1;
      }
    });
  }

  Future<void> _pickFiles() async {
    if (!mounted) return;

    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    final isArabic = localeProvider.locale.languageCode == 'ar';

    // Simulate file selection for demo purposes
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            isArabic ? 'اختيار ملف' : 'Sélectionner un fichier',
          ),
          content: Text(
            isArabic
                ? 'سيتم إضافة وظيفة اختيار الملفات لاحقاً.\nللآن، سيتم إضافة ملف تجريبي.'
                : 'La fonctionnalité de sélection de fichiers sera ajoutée plus tard.\nPour l\'instant, un fichier de démonstration sera ajouté.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(isArabic ? 'إلغاء' : 'Annuler'),
            ),
            ElevatedButton(
              onPressed: () {
                setState(() {
                  _selectedFiles.clear();
                  _selectedFiles.add(isArabic ? 'مستند_تجريبي.pdf' : 'document_demo.pdf');
                });
                Navigator.of(context).pop();
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      isArabic ? 'تم إضافة ملف تجريبي' : 'Fichier de démonstration ajouté',
                    ),
                  ),
                );
              },
              child: Text(isArabic ? 'إضافة ملف تجريبي' : 'Ajouter fichier démo'),
            ),
          ],
        );
      },
    );
  }

  void _submitForm() {
    // Implémenter la logique de soumission du formulaire
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Formulaire soumis avec succès !')),
    );
  }

  @override
  Widget build(BuildContext context) {
    final localeProvider = Provider.of<LocaleProvider>(context);
    final isArabic = localeProvider.locale.languageCode == 'ar';

    return Scaffold(
      backgroundColor: AppColors.backgroundSecondary,
      appBar: AppBar(
        title: Text(
          isArabic ? 'تقديم طلب' : 'Soumettre une demande',
          style: const TextStyle(color: AppColors.textLight),
        ),
        backgroundColor: AppColors.primaryOrange,
        elevation: 0,
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: Constants.screenPadding,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildStepper(isArabic),
              const SizedBox(height: Constants.extraLargeSpacing),
              if (_currentStep == 1) _buildGeneralOwnerInfo(isArabic),
              if (_currentStep == 2) _buildProjectInfo(isArabic),
              if (_currentStep == 3) _buildStep3Documents(isArabic),
              if (_currentStep == 4) _buildStep4Summary(isArabic),
              const SizedBox(height: Constants.extraLargeSpacing),
              _buildActionButtons(isArabic, context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStepper(bool isArabic) {
    final steps = [
      isArabic ? 'بيانات المالك' : 'Propriétaire',
      isArabic ? 'بيانات المشروع' : 'Projet',
      isArabic ? 'المستندات' : 'Documents',
      isArabic ? 'ملخص' : 'Résumé',
    ];

    return Row(
      children: List.generate(steps.length * 2 - 1, (index) {
        if (index.isEven) {
          final stepNumber = index ~/ 2 + 1;
          final isActive = stepNumber == _currentStep;
          return Expanded(
            child: Column(
              children: [
                _buildStep(stepNumber, isActive: isActive),
                const SizedBox(height: 8),
                Text(
                  steps[index ~/ 2],
                  style: TextStyle(
                    color: isActive ? AppColors.primaryOrange : AppColors.darkGray,
                    fontSize: 12,
                    height: 1.2,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          );
        }
        return Container(
          width: 20,
          height: 2,
          color: AppColors.darkGray,
        );
      }),
    );
  }

  Widget _buildStep(int step, {required bool isActive}) {
    return Container(
      width: 32,
      height: 32,
      decoration: BoxDecoration(
        color: isActive ? AppColors.primaryOrange : AppColors.backgroundPrimary,
        shape: BoxShape.circle,
        border: Border.all(
          color: isActive ? AppColors.primaryOrange : AppColors.mediumGray,
          width: 2,
        ),
      ),
      child: Center(
        child: Text(
          step.toString(),
          style: TextStyle(
            color: isActive ? AppColors.textLight : AppColors.textSecondary,
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
      ),
    );
  }

  Widget _buildGeneralOwnerInfo(bool isArabic) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          isArabic ? 'بيانات المالك' : 'Informations du propriétaire',
          style: Constants.sectionTitleStyle,
        ),
        const SizedBox(height: Constants.mediumSpacing),
        Card(
          elevation: Constants.cardTheme.elevation,
          shape: Constants.cardTheme.shape,
          color: Constants.cardTheme.color,
          child: Padding(
            padding: Constants.cardPadding,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CheckboxListTile(
                  title: Text(
                    isArabic ? 'مقدم الطلب هو المالك' : 'Le demandeur est le propriétaire',
                    style: Constants.subtitleStyle,
                  ),
                  value: _isOwnerApplicant,
                  onChanged: (value) => setState(() => _isOwnerApplicant = value ?? false),
                  activeColor: AppColors.primaryOrange,
                ),
                if (!_isOwnerApplicant) ...[
                  const SizedBox(height: Constants.mediumSpacing),
                  _buildTextField(
                    controller: _ownerNameController,
                    label: isArabic ? 'اسم المالك *' : 'Nom du propriétaire *',
                    isArabic: isArabic,
                    validator: (value) => value!.isEmpty
                        ? (isArabic ? 'يرجى إدخال اسم المالك' : 'Veuillez entrer le nom')
                        : null,
                  ),
                  const SizedBox(height: Constants.mediumSpacing),
                  _buildTextField(
                    controller: _ownerAddressController,
                    label: isArabic ? 'العنوان *' : 'Adresse *',
                    isArabic: isArabic,
                    validator: (value) => value!.isEmpty
                        ? (isArabic ? 'يرجى إدخال العنوان' : 'Veuillez entrer l\'adresse')
                        : null,
                  ),
                  const SizedBox(height: Constants.mediumSpacing),
                  _buildTextField(
                    controller: _ownerEmailController,
                    label: isArabic ? 'البريد الإلكتروني' : 'Email',
                    isArabic: isArabic,
                    keyboardType: TextInputType.emailAddress,
                    validator: (value) {
                      if (value != null && value.isNotEmpty) {
                        final emailRegex = RegExp(r'^[^@]+@[^@]+\.[^@]+');
                        if (!emailRegex.hasMatch(value)) {
                          return isArabic ? 'البريد غير صالح' : 'Email invalide';
                        }
                      }
                      return null;
                    },
                  ),
                ],
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildProjectInfo(bool isArabic) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          isArabic ? 'بيانات العقار' : 'Informations de la propriété',
          style: Constants.sectionTitleStyle,
        ),
        const SizedBox(height: Constants.mediumSpacing),
        Card(
          elevation: Constants.cardTheme.elevation,
          shape: Constants.cardTheme.shape,
          color: Constants.cardTheme.color,
          child: Padding(
            padding: Constants.cardPadding,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildTextField(
                  controller: _projectAddressController,
                  label: isArabic ? 'عنوان المشروع *' : 'Adresse du projet *',
                  isArabic: isArabic,
                  validator: (value) => value!.isEmpty
                      ? (isArabic ? 'يرجى إدخال عنوان المشروع' : 'Veuillez entrer l\'adresse')
                      : null,
                ),
                const SizedBox(height: Constants.mediumSpacing),
                _buildTextField(
                  controller: _totalAreaController,
                  label: isArabic ? 'المساحة الإجمالية (م²) *' : 'Surface totale (m²) *',
                  isArabic: isArabic,
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value!.isEmpty) {
                      return isArabic ? 'يرجى إدخال المساحة' : 'Veuillez entrer la surface';
                    }
                    if (double.tryParse(value) == null || double.parse(value) <= 0) {
                      return isArabic ? 'المساحة غير صالحة' : 'Surface invalide';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: Constants.mediumSpacing),
                _buildPropertyTypeDropdown(isArabic),
                const SizedBox(height: Constants.mediumSpacing),
                _buildRequestTypeDropdown(isArabic),
                const SizedBox(height: Constants.mediumSpacing),
                _buildTextField(
                  controller: _requestNatureController,
                  label: isArabic ? 'طبيعة الطلب *' : 'Nature de la demande *',
                  isArabic: isArabic,
                  validator: (value) => value!.isEmpty
                      ? (isArabic ? 'يرجى إدخال طبيعة الطلب' : 'Veuillez entrer la nature')
                      : null,
                ),
                const SizedBox(height: Constants.mediumSpacing),
                _buildTextField(
                  controller: _currentLandUseController,
                  label: isArabic ? 'الاستخدام الحالي للأرض' : 'Utilisation actuelle du terrain',
                  isArabic: isArabic,
                ),
                const SizedBox(height: Constants.mediumSpacing),
                _buildTextField(
                  controller: _existingBuildingsController,
                  label: isArabic
                      ? 'تفاصيل المباني القائمة'
                      : 'Détails des bâtiments existants',
                  isArabic: isArabic,
                  maxLines: 3,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required bool isArabic,
    TextInputType keyboardType = TextInputType.text,
    String? Function(String?)? validator,
    int? maxLines = 1,  // Add this parameter with a default value of 1
  }) {
    return TextFormField(
      controller: controller,
      decoration: InputDecoration(
        labelText: label,
        labelStyle: const TextStyle(color: AppColors.darkGray),
        border: const OutlineInputBorder(),
        enabledBorder: const OutlineInputBorder(
          borderSide: BorderSide(color: AppColors.darkGray),
        ),
        focusedBorder: const OutlineInputBorder(
          borderSide: BorderSide(color: AppColors.primaryOrange, width: 2),
        ),
        errorBorder: const OutlineInputBorder(
          borderSide: BorderSide(color: AppColors.error),
        ),
        filled: true,
        fillColor: AppColors.pureWhite,
      ),
      keyboardType: keyboardType,
      validator: validator,
      textAlign: isArabic ? TextAlign.right : TextAlign.left,
      style: const TextStyle(color: AppColors.pureBlack),
    );
  }

  Widget _buildPropertyTypeDropdown(bool isArabic) {
    final propertyTypes = [
      {'ar': 'سكني', 'fr': 'Résidentiel'},
      {'ar': 'تجاري', 'fr': 'Commercial'},
      {'ar': 'صناعي', 'fr': 'Industriel'},
      {'ar': 'اخرى', 'fr': 'Autre'},
    ];

    return DropdownButtonFormField<String>(
      value: _propertyType,
      decoration: InputDecoration(
        labelText: isArabic ? 'نوع العقار *' : 'Type de propriété *',
        labelStyle: Constants.subtitleStyle,
        border: Constants.defaultBorder,
        focusedBorder: Constants.focusedBorder,
        filled: true,
        fillColor: AppColors.pureWhite,
      ),
      validator: (value) => value == null
          ? (isArabic ? 'يرجى اختيار نوع العقار' : 'Veuillez choisir le type')
          : null,
      items: propertyTypes.map((type) {
        final value = isArabic ? type['ar']! : type['fr']!;
        return DropdownMenuItem<String>(
          value: value,
          child: Text(value),
        );
      }).toList(),
      onChanged: (value) => setState(() => _propertyType = value),
    );
  }

  Widget _buildRequestTypeDropdown(bool isArabic) {
    final requestTypes = [
      {'ar': 'طلب فردي', 'fr': 'Demande individuelle'},
      {'ar': 'طلب غير فردي', 'fr': 'Demande non individuelle'},
    ];

    return DropdownButtonFormField<String>(
      value: _requestType,
      decoration: InputDecoration(
        labelText: isArabic ? 'نوع الطلب *' : 'Type de demande *',
        labelStyle: Constants.subtitleStyle,
        border: Constants.defaultBorder,
        focusedBorder: Constants.focusedBorder,
        filled: true,
        fillColor: AppColors.pureWhite,
      ),
      validator: (value) => value == null
          ? (isArabic ? 'يرجى اختيار نوع الطلب' : 'Veuillez choisir le type')
          : null,
      items: requestTypes.map((type) {
        final value = isArabic ? type['ar']! : type['fr']!;
        return DropdownMenuItem<String>(
          value: value,
          child: Text(value),
        );
      }).toList(),
      onChanged: (value) => setState(() => _requestType = value),
    );
  }

  Widget _buildStep3Documents(bool isArabic) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          isArabic ? 'المستندات المطلوبة' : 'Documents requis',
          style: Constants.sectionTitleStyle,
        ),
        const SizedBox(height: Constants.mediumSpacing),
        Card(
          elevation: Constants.cardTheme.elevation,
          shape: Constants.cardTheme.shape,
          color: Constants.cardTheme.color,
          child: Padding(
            padding: Constants.cardPadding,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildRequiredDocumentsList(isArabic),
                const SizedBox(height: Constants.largeSpacing),
                _buildFileUploadSection(isArabic),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildRequiredDocumentsList(bool isArabic) {
    final documents = isArabic
        ? [
            'صورة طبق الأصل من سند الملكية',
            'مخططات المبنى مصدقة من مهندس معماري',
            'صورة من بطاقة الهوية للمالك ومقدم الطلب',
            'وكالة رسمية موثقة إذا كان مقدم الطلب غير المالك',
            'تصريح من الجهة المختصة للأراضي الزراعية',
          ]
        : [
            'Copie conforme du titre de propriété',
            'Plans du bâtiment certifiés par un architecte',
            'Copie de la carte d\'identité',
            'Procuration officielle notariée si nécessaire',
            'Autorisation pour les terres agricoles',
          ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: documents.map((doc) => Padding(
            padding: const EdgeInsets.symmetric(vertical: 4.0),
            child: Row(
              textDirection: isArabic ? TextDirection.rtl : TextDirection.ltr,
              children: [
                Padding(
                  padding: EdgeInsets.only(
                    top: 6,
                    left: isArabic ? 8 : 0,
                    right: isArabic ? 0 : 8,
                  ),
                  child: Container(
                    width: 6,
                    height: 6,
                    decoration: const BoxDecoration(
                      color: AppColors.info,
                      shape: BoxShape.circle,
                    ),
                  ),
                ),
                Expanded(
                  child: Text(
                    doc,
                    style: Constants.subtitleStyle,
                    textAlign: isArabic ? TextAlign.right : TextAlign.left,
                  ),
                ),
              ],
            ),
          )).toList(),
    );
  }

  Widget _buildFileUploadSection(bool isArabic) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        const Icon(
          Icons.upload_file,
          size: 48,
          color: AppColors.info,
        ),
        const SizedBox(height: Constants.mediumSpacing),
        Text(
          isArabic ? 'رفع الملف' : 'Télécharger le fichier',
          style: Constants.sectionTitleStyle.copyWith(fontSize: 18),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 8),
        Text(
          isArabic
              ? 'انقر لاختيار الملف'
              : 'Cliquez pour sélectionner le fichier',
          style: Constants.subtitleStyle,
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: Constants.mediumSpacing),
        Center(
          child: ElevatedButton(
            onPressed: _pickFiles,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.pureWhite,
              foregroundColor: AppColors.info,
              side: const BorderSide(color: AppColors.info),
              minimumSize: const Size(200, 40),
            ),
            child: Text(isArabic ? 'اختيار ملف' : 'Sélectionner un fichier'),
          ),
        ),
        if (_selectedFiles.isNotEmpty) ...[
          const SizedBox(height: Constants.mediumSpacing),
          Center(
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppColors.lightGray,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    isArabic ? 'الملف المختار:' : 'Fichier sélectionné:',
                    style: Constants.subtitleStyle.copyWith(fontWeight: FontWeight.bold),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.picture_as_pdf,
                        size: 20,
                        color: AppColors.darkGray,
                      ),
                      const SizedBox(width: 8),
                      Flexible(
                        child: Text(
                          _selectedFiles.first,
                          style: Constants.subtitleStyle,
                          overflow: TextOverflow.ellipsis,
                          textAlign: TextAlign.center,
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.close, size: 20),
                        onPressed: () => setState(() => _selectedFiles.clear()),
                        color: AppColors.darkGray,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
        const SizedBox(height: 8),
        Text(
          isArabic
              ? 'ملف PDF فقط (حتى 10 ميجابايت)'
              : 'Fichier PDF uniquement (jusqu\'à 10 Mo)',
          style: Constants.subtitleStyle.copyWith(
            fontSize: 12,
            color: AppColors.darkGray,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildStep4Summary(bool isArabic) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          isArabic ? 'ملخص الطلب' : 'Résumé de la demande',
          style: Constants.sectionTitleStyle,
        ),
        const SizedBox(height: Constants.mediumSpacing),
        Card(
          elevation: Constants.cardTheme.elevation,
          shape: Constants.cardTheme.shape,
          color: Constants.cardTheme.color,
          child: Padding(
            padding: Constants.cardPadding,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildSummaryItem(
                  label: isArabic ? 'اسم المالك' : 'Nom du propriétaire',
                  value: _isOwnerApplicant ? 'نفس مقدم الطلب' : _ownerNameController.text,
                  isArabic: isArabic,
                ),
                _buildSummaryItem(
                  label: isArabic ? 'عنوان المشروع' : 'Adresse du projet',
                  value: _projectAddressController.text,
                  isArabic: isArabic,
                ),
                _buildSummaryItem(
                  label: isArabic ? 'نوع العقار' : 'Type de propriété',
                  value: _propertyType ?? '-',
                  isArabic: isArabic,
                ),
                _buildSummaryItem(
                  label: isArabic ? 'الملفات المرفقة' : 'Fichiers joints',
                  value: _selectedFiles.isEmpty
                      ? (isArabic ? 'لا توجد ملفات' : 'Aucun fichier')
                      : _selectedFiles.length.toString() +
                          (isArabic ? ' ملفات' : ' fichiers'),
                  isArabic: isArabic,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSummaryItem({
    required String label,
    required String value,
    required bool isArabic,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        textDirection: isArabic ? TextDirection.rtl : TextDirection.ltr,
        children: [
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: Constants.subtitleStyle.copyWith(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(
              value,
              style: Constants.subtitleStyle,
              textAlign: isArabic ? TextAlign.right : TextAlign.left,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(bool isArabic, BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        if (_currentStep > 1)
          TextButton(
            onPressed: _previousStep,
            child: Text(
              isArabic ? 'السابق' : 'Précédent',
              style: const TextStyle(color: AppColors.primaryOrange),
            ),
          ),
        const Spacer(), // Ajoute un espace flexible
        ElevatedButton(
          onPressed: _nextStep,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.primaryOrange,
            foregroundColor: AppColors.pureWhite,
          ),
          child: Text(
            _currentStep < 4
                ? (isArabic ? 'التالي' : 'Suivant')
                : (isArabic ? 'إرسال' : 'Soumettre'),
          ),
        ),
      ],
    );
  }
}
