import 'package:droit/src/config/colors.dart';
import 'package:droit/src/widgets/locale_provider.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../config/models/constants.dart';
import 'package:intl/intl.dart';

class NotificationsScreen extends StatefulWidget {
  const NotificationsScreen({super.key});

  @override
  State<NotificationsScreen> createState() => _NotificationsScreenState();
}

class _NotificationsScreenState extends State<NotificationsScreen> {
  final List<Map<String, dynamic>> notifications = [
    {
      'type': 'rejection',
      'requestId': 'DEM-2023-001',
      'date': DateTime.now().subtract(const Duration(days: 1)),
      'message': 'Votre demande a été refusée',
      'messageAr': 'تم رفض طلبك',
      'details': 'Documents incomplets',
      'detailsAr': 'وثائق غير مكتملة',
    },
    {
      'type': 'approval',
      'requestId': 'DEM-2023-002',
      'date': DateTime.now().subtract(const Duration(days: 2)),
      'message': 'Votre demande a été approuvée',
      'messageAr': 'تمت الموافقة على طلبك',
      'details': 'Vous pouvez maintenant procéder au paiement',
      'detailsAr': 'يمكنك الآن المتابعة للدفع',
    },
    {
      'type': 'expiration',
      'requestId': 'DEM-2023-003',
      'date': DateTime.now().subtract(const Duration(days: 3)),
      'message': 'Votre permis expire bientôt',
      'messageAr': 'تصريحك على وشك الانتهاء',
      'details': 'Expiration dans 30 jours',
      'detailsAr': 'ينتهي في غضون 30 يومًا',
      'expiryDate': DateTime.now().add(const Duration(days: 30)),
    },
  ];

  @override
  Widget build(BuildContext context) {
    final isArabic = Provider.of<LocaleProvider>(context).locale.languageCode == 'ar';
    return Scaffold(
      appBar: AppBar(
        title: Text(isArabic ? 'الإشعارات' : 'Notifications'),
        backgroundColor: AppColors.lightGray,
      ),
      body: notifications.isEmpty
          ? Center(
              child: Text(
                isArabic ? 'لا توجد إشعارات' : 'Aucune notification',
                style: Constants.sectionTitleStyle,
              ),
            )
          : ListView.builder(
              padding: const EdgeInsets.all(16.0),
              itemCount: notifications.length,
              itemBuilder: (context, index) {
                return _buildNotificationCard(notifications[index], isArabic);
              },
            ),
    );
  }

  Widget _buildNotificationCard(Map<String, dynamic> notification, bool isArabic) {
    IconData iconData;
    Color iconColor;

    switch (notification['type']) {
      case 'rejection':
        iconData = Icons.cancel_outlined;
        iconColor = AppColors.error;
        break;
      case 'approval':
        iconData = Icons.check_circle_outline;
        iconColor = AppColors.success;
        break;
      case 'expiration':
        iconData = Icons.access_time;
        iconColor = AppColors.warning;
        break;
      default:
        iconData = Icons.notifications_outlined;
        iconColor = AppColors.info;
    }

    return Card(
      elevation: 2,
      margin: const EdgeInsets.only(bottom: 16.0),
      color: AppColors.pureWhite,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  iconData,
                  color: iconColor,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        isArabic ? notification['messageAr'] : notification['message'],
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: iconColor,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '${isArabic ? 'رقم الطلب: ' : 'Demande N°: '}${notification['requestId']}',
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.grey,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              isArabic ? notification['detailsAr'] : notification['details'],
              style: const TextStyle(
                fontSize: 14,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _formatDate(notification['date'], isArabic),
              style: const TextStyle(
                fontSize: 12,
                color: Colors.grey,
              ),
            ),
            if (notification['type'] == 'expiration' && notification['expiryDate'] != null) ...[
              const SizedBox(height: 12),
              _buildExpirationWarning(notification['expiryDate'] as DateTime, isArabic),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildExpirationWarning(DateTime expiryDate, bool isArabic) {
    final daysRemaining = expiryDate.difference(DateTime.now()).inDays;
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.orange.withOpacity(0.15),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.orange.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          const Icon(
            Icons.warning_amber_rounded,
            color: Colors.orange,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              isArabic
                  ? 'متبقي $daysRemaining يوم على انتهاء التصريح'
                  : 'Il reste $daysRemaining jours avant expiration',
              style: const TextStyle(
                color: Colors.orange,
                fontWeight: FontWeight.w600,
                fontSize: 14,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAppealButton(bool isArabic, String requestId) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: () {
          // Gérer l'action du recours ici
          debugPrint('Soumettre un recours pour la demande: $requestId');
        },
        icon: const Icon(Icons.gavel, color: Colors.white),
        label: Text(
          isArabic ? 'تقديم طعن' : 'Soumettre un recours',
          style: const TextStyle(color: Colors.white),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.red,
          padding: const EdgeInsets.symmetric(vertical: 12),
        ),
      ),
    );
  }

  Color _getIconColor(String type) {
    switch (type) {
      case 'rejection':
        return AppColors.error;
      case 'approval':
        return AppColors.success;
      case 'expiration':
        return AppColors.warning;
      default:
        return AppColors.info;
    }
  }

  String _formatDate(DateTime date, bool isArabic) {
    final formatter = DateFormat(isArabic ? 'dd/MM/yyyy HH:mm' : 'dd/MM/yyyy HH:mm');
    return isArabic
        ? 'تاريخ الإشعار: ${formatter.format(date)}'
        : 'Date de notification: ${formatter.format(date)}';
  }
}
