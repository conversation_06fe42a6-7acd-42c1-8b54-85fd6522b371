import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'colors.dart';
import 'theme_provider.dart';

/// Helper class to get theme-aware colors
class ThemeHelper {
  /// Get the appropriate color scheme based on current theme
  static dynamic getColors(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context, listen: false);
    return themeProvider.isDarkMode ? AppColors.dark : AppColors.light;
  }

  /// Get theme-aware colors with listener (for widgets that need to rebuild on theme change)
  static dynamic getColorsWithListener(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    return themeProvider.isDarkMode ? AppColors.dark : AppColors.light;
  }

  /// Check if current theme is dark
  static bool isDarkMode(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context, listen: false);
    return themeProvider.isDarkMode;
  }

  /// Check if current theme is dark with listener
  static bool isDarkModeWithListener(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    return themeProvider.isDarkMode;
  }

  /// Get theme-aware text style
  static TextStyle getTextStyle(BuildContext context, {
    double? fontSize,
    FontWeight? fontWeight,
    Color? color,
    double? height,
    double? letterSpacing,
  }) {
    final colors = getColors(context);
    return TextStyle(
      fontSize: fontSize ?? 16,
      fontWeight: fontWeight ?? FontWeight.normal,
      color: color ?? colors.textPrimary,
      fontFamily: 'Cairo',
      height: height,
      letterSpacing: letterSpacing,
    );
  }

  /// Get theme-aware title text style
  static TextStyle getTitleStyle(BuildContext context) {
    final colors = getColors(context);
    return TextStyle(
      fontSize: 32,
      fontWeight: FontWeight.bold,
      color: colors.textPrimary,
      fontFamily: 'Cairo',
      height: 1.2,
    );
  }

  /// Get theme-aware subtitle text style
  static TextStyle getSubtitleStyle(BuildContext context) {
    final colors = getColors(context);
    return TextStyle(
      fontSize: 18,
      color: colors.textSecondary,
      fontFamily: 'Cairo',
      height: 1.5,
    );
  }

  /// Get theme-aware section title text style
  static TextStyle getSectionTitleStyle(BuildContext context) {
    final colors = getColors(context);
    return TextStyle(
      fontSize: 24,
      fontWeight: FontWeight.bold,
      color: colors.textPrimary,
      fontFamily: 'Cairo',
      height: 1.3,
    );
  }

  /// Get theme-aware button text style
  static TextStyle getButtonTextStyle(BuildContext context) {
    final colors = getColors(context);
    return TextStyle(
      fontSize: 16,
      fontWeight: FontWeight.bold,
      color: colors.textOnPrimary,
      fontFamily: 'Cairo',
      letterSpacing: 0.5,
    );
  }

  /// Get theme-aware input decoration
  static InputDecoration getInputDecoration(BuildContext context, {
    String? labelText,
    String? hintText,
    Widget? prefixIcon,
    Widget? suffixIcon,
  }) {
    final colors = getColors(context);
    return InputDecoration(
      labelText: labelText,
      hintText: hintText,
      prefixIcon: prefixIcon,
      suffixIcon: suffixIcon,
      filled: true,
      fillColor: colors.surface,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(
          color: colors.borderPrimary,
          width: 1.5,
        ),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(
          color: colors.borderPrimary,
          width: 1.5,
        ),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(
          color: colors.borderFocused,
          width: 3.0,
        ),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(
          color: colors.borderError,
          width: 1.5,
        ),
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      labelStyle: TextStyle(
        color: colors.textSecondary,
        fontFamily: 'Cairo',
      ),
      hintStyle: TextStyle(
        color: colors.textSecondary.withOpacity(0.6),
        fontFamily: 'Cairo',
      ),
    );
  }

  /// Get theme-aware card decoration
  static BoxDecoration getCardDecoration(BuildContext context) {
    final colors = getColors(context);
    return BoxDecoration(
      color: colors.card,
      borderRadius: BorderRadius.circular(12),
      boxShadow: [
        BoxShadow(
          color: colors.shadowColor,
          blurRadius: 8,
          offset: const Offset(0, 2),
        ),
      ],
    );
  }

  /// Get theme-aware button style
  static ButtonStyle getPrimaryButtonStyle(BuildContext context) {
    final colors = getColors(context);
    return ElevatedButton.styleFrom(
      backgroundColor: colors.buttonPrimary,
      foregroundColor: colors.textOnPrimary,
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      elevation: 2,
      textStyle: const TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.bold,
        fontFamily: 'Cairo',
        letterSpacing: 0.5,
      ),
    );
  }

  /// Get theme-aware secondary button style
  static ButtonStyle getSecondaryButtonStyle(BuildContext context) {
    final colors = getColors(context);
    return ElevatedButton.styleFrom(
      backgroundColor: colors.buttonSecondary,
      foregroundColor: colors.textOnPrimary,
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      elevation: 2,
      textStyle: const TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.bold,
        fontFamily: 'Cairo',
        letterSpacing: 0.5,
      ),
    );
  }
}
